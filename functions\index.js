const { onSchedule } = require('firebase-functions/v2/scheduler');
const { onCall } = require('firebase-functions/v2/https');
const logger = require('firebase-functions').logger;
const admin = require('firebase-admin');
const serviceAccount = require('./serviceAccountKey.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
});
const db = admin.firestore();

const HOUR = 60 * 60 * 1000;

// resetUserProgressIfInactive (runs daily)
exports.resetUserProgressIfInactive = onSchedule('every 24 hours', async (event) => {
    try {
        logger.info('Starting resetUserProgressIfInactive');
        
        // Check if leaderboard is updating to prevent race conditions
        const flagDoc = await db.collection('flags').doc('bvJSDVy763Blf5iJxeLQ').get();
        if (flagDoc.exists) {
            const flagData = flagDoc.data();
            if (flagData.isLeaderboardUpdating) {
                logger.info('Skipping inactive user reset - leaderboard is updating');
                return;
            }
        }

        const now = new Date();
        const usersSnapshot = await db.collection('users').get();
        const heartsSnapshot = await db.collection('hearts').limit(1).get();

        let defaultHearts = 3;
        
        if (!heartsSnapshot.empty) {
            const firstDoc = heartsSnapshot.docs[0];
            defaultHearts = firstDoc.data().defaultHearts ?? 3;
        }

        let resetCount = 0;
        let batch = db.batch();

        for (const userDoc of usersSnapshot.docs) {
            const userId = userDoc.id;
            const user = userDoc.data();
            const lastActive = user.lastActiveTime ? user.lastActiveTime.toDate() : null;

            if (!lastActive) {
                logger.info(`User ${userId} has no lastActiveTime, skipping`);
                continue;
            }

            const inactiveDays = (now - lastActive) / (1000 * 60 * 60 * 24);

            if (inactiveDays >= 30) {
                logger.info(`Resetting user ${userId} - inactive for ${Math.floor(inactiveDays)} days`);
                
                // Update user document with all necessary fields
                const userRef = db.collection('users').doc(userId);
                batch.update(userRef, {
                    neurons: 0,
                    hearts: defaultHearts,
                    league: 'Bronzana',
                    groupId: admin.firestore.FieldValue.delete(), // Remove groupId
                    weeklyScore: 0,
                    leagueEventsCounter: admin.firestore.FieldValue.increment(1)
                });

                // Remove from current league (if exists and not already Bronzana)
                const currentLeague = user.league || 'Bronzana';
                const currentGroupId = user.groupId;
                
                if (currentLeague !== 'Bronzana' && currentGroupId) {
                    const playerRef = db.collection('leaderboards')
                        .doc(currentLeague)
                        .collection('groups')
                        .doc(currentGroupId)
                        .collection('players')
                        .doc(userId);
                    batch.delete(playerRef);
                }

                resetCount++;
                
                // Commit batch every 450 operations to stay under Firestore limits
                if (resetCount % 450 === 0) {
                    await batch.commit();
                    batch = db.batch(); // Create new batch after commit
                    logger.info(`Committed batch of ${resetCount} user resets`);
                }
            }
        }

        // Commit remaining operations
        if (resetCount % 450 !== 0 && resetCount > 0) {
            await batch.commit();
        }

        logger.info(`resetUserProgressIfInactive completed - reset ${resetCount} users`);
    } catch (error) {
        logger.error('Error resetting inactive user progress:', error);
        // Don't throw to prevent function failures
    }
});

// sendInactivityNotifications (keep it scheduled)
exports.sendInactivityNotifications = onSchedule('every 1 hours', async (event) => {
    try {
        const now = new Date();
        const usersSnapshot = await db.collection('users').get();

        for (const doc of usersSnapshot.docs) {
            const user = doc.data();
            const lastActiveTime = user.lastActiveTime ? user.lastActiveTime.toDate() : null;
            const lastSent = user.lastInactivityNotification || null;

            if (!lastActiveTime) continue;

            const timeSinceLastActive = now - lastActiveTime;
            let message = null;
            let tag = null;

            if (timeSinceLastActive > 48 * HOUR && timeSinceLastActive < 49 * HOUR && lastSent !== '48h') {
                message = '📱Ćao, zar ćeš da koristiš socijalne mreže ceo dan?';
                tag = '48h';
            } else if (timeSinceLastActive > 44 * HOUR && timeSinceLastActive < 45 * HOUR && lastSent !== '44h') {
                message = '🔥Nedozvoli da propustiš bonus neurone!🔥 Imaš još 4 sata da bi ostvario bonus';
                tag = '44h';
            } else if (timeSinceLastActive > 32 * HOUR && timeSinceLastActive < 33 * HOUR && lastSent !== '32h') {
                message = '😔Zar ćeš da dopustiš da ispadneš iz lige?';
                tag = '32h';
            } else if (timeSinceLastActive > 24 * HOUR && timeSinceLastActive < 25 * HOUR && lastSent !== '24h') {
                message = '🧠Ćao, vreme je za jednu lekciju, izdvoji 5 minuta da ispuniš dan novim znanjem!🧠';
                tag = '24h';
            } else if (timeSinceLastActive > 7 * 24 * HOUR && lastSent !== '7d') {
                message = 'Dobro, nećemo ti slati više notifikacije, srećno! 👋';
                tag = '7d';
            }

            if (message && user.isNotificationOn && user.deviceToken) {
                const payload = {
                    notification: {
                        title: 'Reminder',
                        body: message,
                    },
                    token: user.deviceToken,
                };
                await admin.messaging().send(payload);

                // Update the last sent tag
                const updateData = {
                    lastInactivityNotification: tag
                };

                // Also turn off future notifications if it's the final message
                if (tag === '7d') {
                    updateData.isNotificationOn = false;
                }

                await db.collection('users').doc(doc.id).update(updateData);
            }
        }

        logger.info('Inactivity notifications sent successfully.');
    } catch (error) {
        logger.error('Error sending notifications:', error);
    }
});

// resetOpenedQuizesAndArticlesinMonth (keep it scheduled)
exports.resetOpenedQuizesAndArticlesinMonth = onSchedule('every 24 hours', async (event) => {
    try {
        logger.info('Starting resetOpenedQuizesAndArticlesinMonth');
        const now = new Date();
        const usersSnapshot = await db.collection('users').get();

        let resetCount = 0;
        let batch = db.batch();

        for (const userDoc of usersSnapshot.docs) {
            const userId = userDoc.id;
            const userData = userDoc.data();
            const lastListResetTimestamp = userData.lastListResetTimestamp ? userData.lastListResetTimestamp.toDate() : null;

            if (!lastListResetTimestamp || (now - lastListResetTimestamp) >= 30 * 24 * 60 * 60 * 1000) {
                const userRef = db.collection('users').doc(userId);
                batch.update(userRef, {
                    openedQuizesAndArticlesinMonth: admin.firestore.FieldValue.delete(),
                    lastListResetTimestamp: admin.firestore.FieldValue.serverTimestamp(),
                });

                resetCount++;
                
                // Commit batch every 450 operations to stay under Firestore limits
                if (resetCount % 450 === 0) {
                    await batch.commit();
                    batch = db.batch(); // Create new batch after commit
                    logger.info(`Committed batch of ${resetCount} quiz/article resets`);
                }
            }
        }

        // Commit remaining operations
        if (resetCount % 450 !== 0 && resetCount > 0) {
            await batch.commit();
        }

        logger.info(`resetOpenedQuizesAndArticlesinMonth completed - reset ${resetCount} users`);
    } catch (error) {
        logger.error('Error resetting opened quizzes and articles:', error);
    }
});

const LEAGUES = [
    'Bronzana', 'Srebrna', 'Zlatna', 'Platinum', 'Dijamantska', 'Rubin',
    'Safirna', 'Smaragdna', 'Topaz', 'Ametist', 'Opal', 'Kvarc', 'Žad',
    'Kristalna', 'Fenix', 'Oniks', 'Galaktička', 'Koralska', 'Jupiter', 'Elitna'
];

const ACHIEVEMENTS = {
    'Bronzana': { id: 'bronze_league_achiever', neurons: 300 },
    'Srebrna': { id: 'silver_league_achiever', neurons: 500 },
    'Zlatna': { id: 'gold_league_achiever', neurons: 1000 },
};

const TITLES = [
    { name: 'Početnik', xpRequired: 3000 },
    { name: 'Pripravnik', xpRequired: 6000 },
    { name: 'Vešt', xpRequired: 10000 },
    { name: 'Stručnjak', xpRequired: 15000 },
    { name: 'Specijalista', xpRequired: 20000 },
    { name: 'Ekspert', xpRequired: 30000 },
    { name: 'Veteran', xpRequired: 40000 },
    { name: 'Majstor', xpRequired: 50000 },
    { name: 'Virtuoz', xpRequired: 60000 },
    { name: 'Mudrac', xpRequired: 70000 },
    { name: 'Velemajstor', xpRequired: 80000 },
    { name: 'Superstar', xpRequired: 90000 },
];



// Function to generate a unique username based on name only
async function generateUniqueUsername(firestore, uid, name) {
    const usersRef = firestore.collection('users');
    const baseName = name.toLowerCase().replaceAll(' ', '');

    let suffix = 1;
    let uniqueUsername;

    while (true) {
        uniqueUsername = `@${baseName}-${suffix}`;
        const existing = await usersRef.where('uniqueName', 'isEqualTo', uniqueUsername).get();

        if (existing.docs.length === 0) {
            break;
        }
        suffix++;
    }

    // Update the user's document with the unique username
    await usersRef.doc(uid).update({
        uniqueName: uniqueUsername,
        updatedAt: admin.firestore.Timestamp.now(),
    });

    logger.info(`Generated unique username ${uniqueUsername} for user ${uid}`);
    return uniqueUsername;
}


// Manual leaderboard update (commented out - now using automatic scheduling)
// exports.manualUpdateLeaderboard = onCall(async (data, context) => {
//     try {
//         logger.info('Starting manualUpdateLeaderboard');
//         await updateLeaderboard();
//         logger.info('manualUpdateLeaderboard completed successfully');
//         return { status: 'success', message: 'Leaderboard manually updated successfully.' };
//     } catch (error) {
//         logger.error('Error in manualUpdateLeaderboard:', error);
//         throw new Error(`Failed to update leaderboard: ${error.message}`);
//     }
// });

// Automatic leaderboard update (runs every Monday at 12:00 AM)
exports.automaticUpdateLeaderboard = onSchedule('0 0 * * 1', async (event) => {
    try {
        logger.info('Starting automatic leaderboard update');
        await updateLeaderboard();
        logger.info('Automatic leaderboard update completed successfully');
    } catch (error) {
        logger.error('Error in automatic leaderboard update:', error);
        // Don't throw to prevent function failures
    }
});
async function updateLeaderboard() {
    const firestore = admin.firestore();
    logger.info('Starting updateLeaderboard');
    
    try {
        // Set leaderboard updating flag
        await firestore.collection('flags').doc('bvJSDVy763Blf5iJxeLQ').set({
            isLeaderboardUpdating: true
        }, { merge: true });

        // Step 1: Fetch users
    logger.info('Fetching users from users collection');
    const usersSnapshot = await firestore.collection('users').get();
    const users = usersSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    logger.info(`Fetched ${users.length} users`);

  // Step 2: Ensure all users have a unique username
    logger.info('Ensuring unique usernames for all users');
    for (let user of users) {
        if (!user.uniqueName || user.uniqueName === 'Unknown') {
            user.uniqueName = await generateUniqueUsername(
                firestore,
                user.uid,
                user.name, 
            );
        }
    }

    // Step 3: Fetch players
    const groups = {};
    LEAGUES.forEach(league => { groups[league] = []; });
    for (const league of LEAGUES) {
        const groupsSnapshot = await firestore
            .collection('leaderboards')
            .doc(league)
            .collection('groups')
            .get();
        logger.info(`Found ${groupsSnapshot.size} groups in ${league}`);
        for (const groupDoc of groupsSnapshot.docs) {
            const playersSnapshot = await groupDoc.ref.collection('players').get();
            logger.info(`Found ${playersSnapshot.size} players in ${league}/${groupDoc.id}`);
            playersSnapshot.docs.forEach(doc => {
                groups[league].push({
                    id: doc.id,
                    groupId: groupDoc.id,
                    ...doc.data()
                });
            });
        }
    }

    // Step 4: Promote/Demote in Transaction
    await firestore.runTransaction(async (transaction) => {
        for (const league of LEAGUES) {
            const groupsSnapshot = await firestore
                .collection('leaderboards')
                .doc(league)
                .collection('groups')
                .get();
            for (const groupDoc of groupsSnapshot.docs) {
                const playersSnapshot = await groupDoc.ref.collection('players')
                    .orderBy('weeklyScore', 'desc')
                    .orderBy('lastUpdated', 'asc')
                    .get();
                const players = playersSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                logger.info(`Processing ${players.length} players in ${league}/${groupDoc.id}`);
                if (players.length === 0) continue;

               const activePlayers = players.filter(player => (player.weeklyScore || 0) > 0);
const inactivePlayers = players.filter(player => (player.weeklyScore || 0) === 0);

const top3 = activePlayers.slice(0, Math.min(3, activePlayers.length));
const bottom3 = activePlayers.slice(-Math.min(3, activePlayers.length));

                if (league !== 'Elitna') {
                    for (const player of top3) {
                        if (player.weeklyScore > 0) {
                            await promotePlayer(player, league, groupDoc.id, LEAGUES, firestore, transaction);
                        } else {
                            logger.info(`Skipping promotion for player ${player.id} due to score: ${player.weeklyScore}`);
                        }
                    }
                }

                if (league !== 'Bronzana') {
                    const playersToDemote = new Set([...bottom3, ...inactivePlayers].filter(player => !top3.some(t => t.id === player.id)));
                    for (const player of playersToDemote) {
                        await demotePlayer(player, league, groupDoc.id, LEAGUES, firestore, transaction);
                    }
                }

                // Send notifications to players who stayed in the same league
                const promotedPlayerIds = new Set(top3.map(p => p.id));
                // Only include players who can actually be demoted (not in Bronzana)
                const actuallyDemotedPlayerIds = new Set();
                if (league !== 'Bronzana') {
                    const playersToDemote = [...bottom3, ...inactivePlayers].filter(player => !top3.some(t => t.id === player.id));
                    playersToDemote.forEach(p => actuallyDemotedPlayerIds.add(p.id));
                }
                const demotedPlayerIds = actuallyDemotedPlayerIds;
                const stayedPlayers = players.filter(player => 
                    !promotedPlayerIds.has(player.id) && !demotedPlayerIds.has(player.id)
                );
                
               logger.info(`League ${league}: ${players.length} total, ${top3.length} promoted, ${demotedPlayerIds.size} demoted, ${stayedPlayers.length} stayed`);
                logger.info(`stayedPlayers ${stayedPlayers}`);
                logger.info(`promotedPlayerIds ${promotedPlayerIds}`);
                logger.info(`demotedPlayerIds ${demotedPlayerIds}`);
                
                for (const player of stayedPlayers) {
                    logger.info(`Notifying ${player.id} that they stayed in ${league}`);
                    await notifyPlayerStayed(player, league, firestore);
                }

                for (const player of players) {
                    const playerRef = groupDoc.ref.collection('players').doc(player.id);
                    transaction.delete(playerRef);
                }
                if (players.length > 0) {
                    transaction.delete(groupDoc.ref);
                }
            }
        }
    });



    // Step 5: Reassignment
    logger.info('Starting group reassignment');
    const updatedGroups = {};
    LEAGUES.forEach(league => { updatedGroups[league] = []; });

    const updatedUsersSnapshot = await firestore.collection('users').get();
    const updatedUsers = updatedUsersSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    logger.info(`Fetched ${updatedUsers.length} users for group reassignment`);

    for (const user of updatedUsers) {
        // Ensure unique username during reassignment
        let username = user.uniqueName;
        if (!username || username === 'Unknown') {
            username = await generateUniqueUsername(
                firestore,
                user.uid,
                user.name,
            );
        }

        const league = user.league || 'Bronzana';
        logger.info(`User ${user.id} has league: ${league}`);
        updatedGroups[league].push({
            id: user.id,
            username: username,
            league: league,
            weeklyScore: 0,
            playerId: user.id
        });
    }

    for (const league of LEAGUES) {
        const leaguePlayers = updatedGroups[league].sort((a, b) => a.id.localeCompare(b.id));
        logger.info(`Assigning ${leaguePlayers.length} players in ${league}`);
        let groupIndex = 1;
        let currentGroup = [];
        for (const player of leaguePlayers) {
            if (currentGroup.length < 10) {
                currentGroup.push(player);
            } else {
                await assignGroup(firestore, league, `group_${groupIndex}`, currentGroup);
                logger.info(`Assigned group_${groupIndex} in ${league} with ${currentGroup.length} players`);
                groupIndex++;
                currentGroup = [player];
            }
        }
        if (currentGroup.length > 0) {
            await assignGroup(firestore, league, `group_${groupIndex}`, currentGroup);
            logger.info(`Assigned group_${groupIndex} in ${league} with ${currentGroup.length} players`);
        }
    }

        // Clear leaderboard updating flag
        await firestore.collection('flags').doc('bvJSDVy763Blf5iJxeLQ').set({
            isLeaderboardUpdating: false
        }, { merge: true });
        
        logger.info('updateLeaderboard completed');
    } catch (error) {
        // Clear leaderboard updating flag even if there's an error
        await firestore.collection('flags').doc('bvJSDVy763Blf5iJxeLQ').set({
            isLeaderboardUpdating: false
        }, { merge: true });
        
        logger.error('Error in updateLeaderboard:', error);
        throw error;
    }
}
    async function assignGroup(firestore, league, groupId, players) {
    const batch = firestore.batch();
    const groupRef = firestore
        .collection('leaderboards')
        .doc(league)
        .collection('groups')
        .doc(groupId);

    batch.set(groupRef, {
        createdAt: admin.firestore.Timestamp.now(),
        league: league,
        groupId: groupId
    });

    for (const player of players) {
        const playerRef = groupRef.collection('players').doc(player.id);
        batch.set(playerRef, {
            username: player.username || 'Unknown',
            league: league,
                       groupId: groupId,
            weeklyScore: player.weeklyScore || 0,
            playerId: player.id,
            lastUpdated: admin.firestore.Timestamp.now(),
        });
        batch.update(firestore.collection('users').doc(player.id), {
            groupId: groupId,
        });
    }

    await batch.commit();
    logger.info(`Assigned ${players.length} players to ${league}/${groupId}`);

    // Verify league field after assignment
    for (const player of players) {
        const userDoc = await firestore.collection('users').doc(player.id).get();
        logger.info(`Post-assignment league for ${player.id}: ${userDoc.data().league}`);
    }
}
async function promotePlayer(player, currentLeague, groupId, leagueOrder, firestore, transaction) {
    const nextLeague = leagueOrder[leagueOrder.indexOf(currentLeague) + 1];
    if (!nextLeague) {
        logger.info(`No next league for ${currentLeague}, skipping promotion for ${player.playerId}`);
        return;
    }

    const userRef = firestore.collection('users').doc(player.playerId);
    logger.info(`Promoting player ${player.playerId} from ${currentLeague} to ${nextLeague}`);

    transaction.update(userRef, {
        league: nextLeague,
        leagueEventsCounter: admin.firestore.FieldValue.increment(1),
        lastPromotion: admin.firestore.Timestamp.now(),
    });

    const userDoc = await userRef.get();
    if (!userDoc.exists) {
        logger.warn(`User ${player.playerId} not found for notification`);
        return;
    }
    const deviceToken = userDoc.data().deviceToken;
    if (deviceToken) {
        await sendNotification(deviceToken, 'Čestitamo!', `Promovisani ste u ${nextLeague}!`);
    }

    logger.info(`Player ${player.playerId} promoted from ${currentLeague} to ${nextLeague}`);
}

async function demotePlayer(player, currentLeague, groupId, leagueOrder, firestore, transaction) {
    const prevLeague = leagueOrder[leagueOrder.indexOf(currentLeague) - 1];
    if (!prevLeague) {
        logger.info(`No previous league for ${currentLeague}, skipping demotion for ${player.playerId}`);
        return;
    }

    const userRef = firestore.collection('users').doc(player.playerId);
    logger.info(`Demoting player ${player.playerId} from ${currentLeague} to ${prevLeague}`);

    transaction.update(userRef, {
        league: prevLeague,
        leagueEventsCounter: admin.firestore.FieldValue.increment(1),
        lastDemotion: admin.firestore.Timestamp.now(),
    });

    const notificationRef = firestore
        .collection('notifications')
        .doc(player.playerId)
        .collection('messages')
        .doc();
    transaction.set(notificationRef, {
        message: `Nažalost, vraćeni ste u ${prevLeague}.`,
        timestamp: admin.firestore.Timestamp.now(),
    });

    const userDoc = await userRef.get();
    if (!userDoc.exists) {
        logger.warn(`User ${player.playerId} not found for notification`);
        return;
    }
    const deviceToken = userDoc.data().deviceToken;
    if (deviceToken) {
        await sendNotification(deviceToken, 'Nažalost!', `Vraćeni ste u ${prevLeague}.`);
    }

    logger.info(`Player ${player.playerId} demoted from ${currentLeague} to ${prevLeague}`);
}
async function notifyPlayerStayed(player, league, firestore) {
    try {
        const userRef = firestore.collection('users').doc(player.playerId);
        const userDoc = await userRef.get();
        
        if (!userDoc.exists) {
            logger.warn(`User ${player.playerId} not found for notification`);
            return;
        }
        
        const deviceToken = userDoc.data().deviceToken;
        if (deviceToken) {
            await sendNotification(deviceToken, 'Trenutna pozicija', `Ostali ste u ${league} za ovu nedelju.`);
        }
        
        logger.info(`Player ${player.playerId} notified about staying in ${league}`);
    } catch (error) {
        logger.error(`Failed to notify player ${player.playerId}:`, error);
    }
}

async function sendNotification(token, title, body) {
    if (!token) {
        logger.info('No device token provided, skipping notification');
        return;
    }

    const message = {
        notification: { title, body },
        token
    };

    try {
        await admin.messaging().send(message);
        logger.info(`Notification sent to ${token}`);
    } catch (error) {
        logger.error(`Failed to send notification to ${token}:`, error);
    }
}


// // 🔥 TEMP: Run only when this file is executed directly (not in production)
// if (require.main === module) {
//   updateLeaderboard()
//     .then(() => {
//       console.log('✅ Leaderboard updated manually (local)');
//       process.exit(0);
//     })
//     .catch((err) => {
//       console.error('❌ Error updating leaderboard:', err);
//       process.exit(1);
//     });
// }